import { http } from '@/apis';
import type { APIResponse, ProofListItem } from '@/utils/type';

interface page {
  current: number;
  limit: number;
}
export interface params2GetKlgList extends page {
  areaCode: string;
  hasPre: number;
  inDoor?: number; // 入口
  keyword?: string; // 知识标题
  name?: string; // 作者（获取全部列表时用）
  number: string; // 编号（获取全部列表时用）
  range: number; // 查询范围
  sortBy: number; //
  sortOrder: number;
  status: string; // 审核状态
  tagId?: number; // 知识标签id
  type: string; // 知识类型
}
export interface params2SubmitKlg {
  cnt: string;
  klgCode: string;
  klgRefVos: klgToRef[];
  notice: string | null;
  sortId: number;
  sortTitle: string;
  status: number;
  sysTitles: string;
  tagIds: string;
  title: string;
  whoName: string;
}
export interface klgToRef {
  cntName: string;
  indexPage: string;
  kid: string;
  refId: string;
}
interface RefVo {
  refId: number;
  indexPage: string;
}
export interface params2EditBaseInfo {
  klgCode: string;
  sortId: number;
  title: string;
  sysTitles: string;
  notice: string;
  areaCodes: string[];
}
export interface params2EditCntInfo {
  klgCode: string;
  cnt: string;
  areaCodes: string[];
  klgToRefVos: RefVo[];
  status: number; // 存草稿: 0 | 提交: 1
}
export interface params2EditProofBlockList {
  klgCode: string;
  klgProofBlockList: ProofListItem[];
  status: number; // 存草稿: 0 | 提交: 1
}
export interface params2GetPreKlgList extends page {
  klgCode: string;
  questionStatus?: number;
  questionType?: string; // 问题类型（1是什么/2为什么/3怎么做/4开放性问题）
  keyword?: string; // 关键字
}
export interface params2GetRecycleKlgList extends page {
  type: string;
  keyword: string;
}
export interface params2GetOuterList extends page {
  keyword: string;
}
export interface params2GetLinkList {
  keyword: string;
  klgCode: string;
}
export interface params2Relation {
  questionId: number | null;
  associatedWords: string;
  keyword: string;
  sort: number;
  sourceId: string;
  targetId: string[];
}
export interface params2Ques {
  questionList?: any[];
  questionId?: number;
}
export interface areaToKlgsListItem {
  clickAreaCode: string;
  klgCodeList: { klgCode: string; title: string }[] | string[];
}
export interface params2MulOpKlg {
  targetAreaCode: string;
  areaToKlgsList: areaToKlgsListItem[];
}

export interface params2Invalid {
  questionIdList: number[];
}
export interface QuestionData {
  /**
   * 关联文本内容
   */
  associatedWords: string;
  /**
   * 问题ID
   */
  questionId: String;
}

// 获取当前领域klg
export function getKlgListApi(params: params2GetKlgList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/getMyList`,
    data: params
  });
}
// 获取当前领域下我的klg
export function getMyKlgListApi(params: params2GetKlgList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/getOneList`,
    data: params
  });
}
// 复制Klg
export function copyKlgApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/copy/${id}`
  });
}
// 提交Klg
export function submitKlgApi(params: params2SubmitKlg): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/edit`,
    data: params
  });
}
// 撤回Klg
export function sendBackKlgApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/back/${id}`
  });
}
// 删除Klg
export function deleteKlgApi(idList: string[]): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/delete/`,
    data: idList
  });
}
// 获取Klg详情
export function getKlgDetailApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/getDetail/${id}`,
    skipDfs: true // 跳过整个接口的DFS处理
  });
}
// 获取klg的参考文献列表
export function getKlgRefListApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg2ref/getList/${id}`
  });
}
// 编辑基础信息
export function editBaseInfoApi(params: params2EditBaseInfo): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/basicCreate`,
    data: params
  });
}
// 编辑内容信息
export function editCntInfoApi(params: params2EditCntInfo): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/contentCreate`,
    data: params
  });
}
// 编辑论证块
export function editProofBlockListApi(params: params2EditProofBlockList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/proof/block/create`,
    data: params
  });
}
// 获取问题列表
// export function getPreKlgQuesListApi(params: params2GetPreKlgList): Promise<APIResponse> {
//   return http.request({
//     method: "post",
//     url: `/klg/question/getQuestionList`,
//     data: params,
//   })
// }
export function getPreKlgQuesListApi(params: params2GetPreKlgList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/page`,
    data: params
  });
}
// 获取前驱知识列表
export function getPreKlgListApi(params: params2GetPreKlgList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/preKlgList`,
    data: params
  });
}
// 获取全量问题列表
export function getQuestionListApi(id: String): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/question/query?klgCode=${id}`
  });
}

// 获取论证列表
export function getProofBlockListApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/proof/block/info?klgCode=${id}`
  });
}
// 获取回收知识列表
export function getRecycleKlgListApi(params: params2GetRecycleKlgList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/getMyDeleteList`,
    data: params
  });
}
// 恢复知识
export function rebackKlgApi(list: string[]): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/reBack`,
    data: list
  });
}
// 销毁知识
export function delKlgApi(list: string[]): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/delList`,
    data: list
  });
}
// 获取外部知识
export function getOuterKlgListApi(params: params2GetOuterList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/getList`,
    data: params
  });
}
// 提交知识
export function submitKlgInTableApi(id: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/klg/submit?klgCode=${id}`
  });
}
// 获取关联列表
export function getLinkListApi(params: params2GetLinkList): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/getCorrelationList`,
    data: params
  });
}
// 自动关联
export function autoRelationApi(klgCode: string): Promise<APIResponse> {
  return http.request({
    method: 'get',
    url: `/relation/doAutoConn/${klgCode}`
  });
}
// 划词关联
export function relationKlgApi(params: params2Relation): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/relation/drawConnect`,
    data: params
  });
}
// 删除问题
export function deleteQuesApi(params: params2Ques): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/delete/batch`,
    data: params
  });
}
// 发布问题
export function publishQuesApi(params: params2Ques): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/doPublish`,
    data: params
  });
}
// 获取问题的详细信息
export function getQuesDetailApi(id: string[]): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/detail`,
    data: {
      questionIdList: id
    }
  });
}
// 撤回发布问题
export function rebackQuesApi(params: params2Ques): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/doRemove`,
    data: params
  });
}
// 批量移入
export function move4AreaApi(params: params2MulOpKlg): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/moveKlgForArea`,
    data: params
  });
}
// 批量添入
export function add4AreaApi(params: params2MulOpKlg): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/addKlgForArea`,
    data: params
  });
}
// 批量上传
export function uploadFileApi(params: any): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/export/more/exportExcel`,
    data: params
  });
}

// 生效/失效问题
export function invalidQuestionApi(params: params2Invalid): Promise<APIResponse> {
  return http.request({
    method: 'post',
    url: `/klg/question/doInvalid`,
    data: params
  });
}
